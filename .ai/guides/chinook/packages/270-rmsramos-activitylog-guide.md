# 1. RmsRamos ActivityLog Integration Guide

> **Package Source:** [rmsramos/activitylog](https://github.com/rmsramos/activitylog)  
> **Official Documentation:** [Filament ActivityLog Plugin Documentation](https://github.com/rmsramos/activitylog/blob/main/README.md)  
> **Laravel Version:** 12.x compatibility  
> **Chinook Integration:** Enhanced for Chinook database schema and entity prefixing  
> **Last Updated:** 2025-07-13

## 1.1. Table of Contents

- [1.2. Overview](#12-overview)
- [1.3. Installation & Configuration](#13-installation--configuration)
  - [1.3.1. Plugin Registration](#131-plugin-registration)
  - [1.3.2. ActivityLog Configuration](#132-activitylog-configuration)
- [1.4. Chinook Model Integration](#14-chinook-model-integration)
  - [1.4.1. Model Activity Tracking](#141-model-activity-tracking)
  - [1.4.2. Custom Activity Logging](#142-custom-activity-logging)
- [1.5. Filament Admin Interface](#15-filament-admin-interface)
- [1.6. Performance & Security](#16-performance--security)

## 1.2. Overview

> **Implementation Note:** This guide adapts the official [RmsRamos ActivityLog documentation](https://github.com/rmsramos/activitylog/blob/main/README.md) for Laravel 12 and Chinook project requirements, integrating with the existing [spatie/laravel-activitylog](160-spatie-activitylog-guide.md) foundation.

**RmsRamos ActivityLog** provides a comprehensive Filament admin interface for viewing and managing activity logs generated by the Spatie Laravel ActivityLog package. It offers enhanced visualization, filtering, and management capabilities for audit trails.

### 1.2.1. Key Features

- **Enhanced Filament Interface**: Rich admin panel for activity log management
- **Advanced Filtering**: Filter by user, model, event type, and date ranges
- **Detailed Activity Views**: Comprehensive display of activity details and changes
- **Bulk Operations**: Mass management of activity log entries
- **Export Capabilities**: Export activity logs for compliance and reporting
- **Real-time Updates**: Live activity monitoring and notifications

### 1.2.2. Integration with Spatie ActivityLog

> **Foundation Package:** Built on [spatie/laravel-activitylog](160-spatie-activitylog-guide.md) with enhanced UI

**Package Relationship:**
- **Spatie ActivityLog**: Provides core activity logging functionality
- **RmsRamos ActivityLog**: Provides Filament admin interface and enhanced features
- **Combined Workflow**: Automatic logging with comprehensive management interface

## 1.3. Installation & Configuration

### 1.3.1. Plugin Registration

> **Configuration Source:** Based on [official installation guide](https://github.com/rmsramos/activitylog#installation)  
> **Chinook Enhancement:** Already configured in AdminPanelProvider

The plugin is already registered in the admin panel. Verify configuration:

<augment_code_snippet path="app/Providers/Filament/AdminPanelProvider.php" mode="EXCERPT">
````php
<?php

namespace App\Providers\Filament;

use Filament\Panel;
use Filament\PanelProvider;
use RmsRamos\Activitylog\ActivitylogPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            // ... existing configuration ...
            
            // RmsRamos ActivityLog Plugin
            ->plugin(
                ActivitylogPlugin::make()
                    ->navigationGroup('Security Management')
                    ->navigationSort(20)
                    ->navigationCountBadge(true)
                    ->resource(\RmsRamos\Activitylog\Resources\ActivitylogResource::class)
            );
    }
}
````
</augment_code_snippet>

### 1.3.2. ActivityLog Configuration

> **Configuration Source:** Enhanced from [spatie/laravel-activitylog configuration](160-spatie-activitylog-guide.md)  
> **Chinook Modifications:** Optimized for Chinook entity tracking and SQLite performance

<augment_code_snippet path="config/activitylog.php" mode="EXCERPT">
````php
<?php
// Configuration enhanced from: https://github.com/spatie/laravel-activitylog/blob/main/config/activitylog.php
// Chinook modifications: Enhanced for Chinook entity prefixing and performance optimization
// Laravel 12 updates: Modern syntax and framework patterns

return [
    /*
     * Activity log model configuration
     */
    'activity_model' => \Spatie\Activitylog\Models\Activity::class,

    /*
     * Table name for activity logs
     */
    'table_name' => 'activity_log',

    /*
     * Database connection for activity logs
     * Using default connection optimized for SQLite WAL mode
     */
    'database_connection' => env('ACTIVITY_LOGGER_DB_CONNECTION'),

    /*
     * Subject returns for activity logging
     */
    'subject_returns_soft_deleted_models' => false,

    /*
     * Enable logging only dirty attributes
     */
    'log_only_dirty' => true,

    /*
     * Submit empty logs (logs without changes)
     */
    'submit_empty_logs' => false,

    /*
     * Chinook-specific configuration
     */
    'chinook' => [
        /*
         * Enable enhanced logging for Chinook entities
         */
        'enable_enhanced_logging' => true,

        /*
         * Track specific Chinook model events
         */
        'tracked_events' => [
            'created', 'updated', 'deleted', 'restored',
            'media_attached', 'media_detached',
            'taxonomy_attached', 'taxonomy_detached',
        ],

        /*
         * Chinook models to track
         */
        'tracked_models' => [
            \App\Models\ChinookArtist::class,
            \App\Models\ChinookAlbum::class,
            \App\Models\ChinookTrack::class,
            \App\Models\ChinookPlaylist::class,
            \App\Models\ChinookCustomer::class,
            \App\Models\ChinookInvoice::class,
        ],

        /*
         * Performance optimization for SQLite
         */
        'batch_size' => 100,
        'queue_activities' => env('QUEUE_ACTIVITY_LOGS', true),
        'retention_days' => env('ACTIVITY_LOG_RETENTION_DAYS', 365),
    ],

    /*
     * Default log name for activities
     */
    'default_log_name' => 'default',

    /*
     * Default authentication driver
     */
    'default_auth_driver' => null,

    /*
     * Enable automatic cleanup of old activity logs
     */
    'delete_records_older_than_days' => 365,

    /*
     * Disable logging when running in console
     */
    'disable_logging_when_running_in_console' => false,
];
````
</augment_code_snippet>

## 1.4. Chinook Model Integration

### 1.4.1. Model Activity Tracking

> **Model Integration:** Enhanced activity tracking for all Chinook entities

<augment_code_snippet path="app/Models/ChinookArtist.php" mode="EXCERPT">
````php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomies;

class ChinookArtist extends Model
{
    use SoftDeletes, LogsActivity, HasTaxonomies;

    protected $table = 'chinook_artists';

    protected $fillable = [
        'name',
        'biography',
        'website',
        'is_active',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'metadata' => 'array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Configure activity logging options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name', 'biography', 'website', 'is_active', 'metadata'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => "Artist {$eventName}")
            ->useLogName('chinook_artists');
    }

    /**
     * Custom activity description
     */
    public function getDescriptionForEvent(string $eventName): string
    {
        return match($eventName) {
            'created' => "Artist '{$this->name}' was created",
            'updated' => "Artist '{$this->name}' was updated",
            'deleted' => "Artist '{$this->name}' was deleted",
            'restored' => "Artist '{$this->name}' was restored",
            default => "Artist '{$this->name}' was {$eventName}",
        };
    }
}
````
</augment_code_snippet>

### 1.4.2. Custom Activity Logging

> **Custom Activities:** Enhanced logging for Chinook-specific business events

<augment_code_snippet path="app/Services/ChinookActivityLogger.php" mode="EXCERPT">
````php
<?php

namespace App\Services;

use Spatie\Activitylog\Facades\CauserResolver;
use Spatie\Activitylog\Models\Activity;

class ChinookActivityLogger
{
    /**
     * Log album purchase activity
     */
    public static function logAlbumPurchase($album, $customer, $invoice): void
    {
        activity('chinook_purchases')
            ->causedBy($customer)
            ->performedOn($album)
            ->withProperties([
                'invoice_id' => $invoice->id,
                'purchase_amount' => $invoice->total,
                'purchase_date' => now(),
                'payment_method' => $invoice->payment_method,
            ])
            ->log("Album '{$album->title}' purchased by {$customer->name}");
    }

    /**
     * Log playlist creation activity
     */
    public static function logPlaylistCreated($playlist, $user): void
    {
        activity('chinook_playlists')
            ->causedBy($user)
            ->performedOn($playlist)
            ->withProperties([
                'track_count' => $playlist->tracks()->count(),
                'is_public' => $playlist->is_public,
            ])
            ->log("Playlist '{$playlist->name}' created");
    }

    /**
     * Log media upload activity
     */
    public static function logMediaUpload($model, $media, $collection): void
    {
        activity('chinook_media')
            ->causedBy(auth()->user())
            ->performedOn($model)
            ->withProperties([
                'media_id' => $media->id,
                'collection' => $collection,
                'file_name' => $media->file_name,
                'file_size' => $media->size,
                'mime_type' => $media->mime_type,
            ])
            ->log("Media '{$media->name}' uploaded to {$collection} collection");
    }

    /**
     * Log taxonomy assignment activity
     */
    public static function logTaxonomyAssignment($model, $taxonomy, $terms): void
    {
        activity('chinook_taxonomy')
            ->causedBy(auth()->user())
            ->performedOn($model)
            ->withProperties([
                'taxonomy_name' => $taxonomy->name,
                'terms_assigned' => $terms->pluck('name')->toArray(),
                'terms_count' => $terms->count(),
            ])
            ->log("Taxonomy '{$taxonomy->name}' assigned to {$model->name}");
    }
}
````
</augment_code_snippet>

## 1.5. Filament Admin Interface

> **Admin Interface:** Enhanced activity log management with filtering and export capabilities

<augment_code_snippet path="app/Filament/Admin/Resources/ActivityLogResource.php" mode="EXCERPT">
````php
<?php

namespace App\Filament\Admin\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Resources\Resource;
use Spatie\Activitylog\Models\Activity;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class ActivityLogResource extends Resource
{
    protected static ?string $model = Activity::class;
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?string $navigationGroup = 'Security Management';
    protected static ?string $navigationLabel = 'Activity Logs';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('log_name')
                    ->label('Log Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'chinook_artists' => 'success',
                        'chinook_albums' => 'info',
                        'chinook_tracks' => 'warning',
                        'chinook_purchases' => 'danger',
                        default => 'gray',
                    })
                    ->sortable()
                    ->searchable(),

                TextColumn::make('description')
                    ->label('Activity')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    })
                    ->searchable(),

                TextColumn::make('subject_type')
                    ->label('Model')
                    ->formatStateUsing(fn (string $state): string =>
                        class_basename($state)
                    )
                    ->sortable(),

                TextColumn::make('causer.name')
                    ->label('User')
                    ->default('System')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label('Date')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('properties')
                    ->label('Changes')
                    ->formatStateUsing(function ($state) {
                        if (empty($state)) return '-';

                        $changes = collect($state)->map(function ($value, $key) {
                            if (is_array($value)) {
                                return $key . ': ' . json_encode($value);
                            }
                            return $key . ': ' . $value;
                        })->take(3)->implode(', ');

                        return strlen($changes) > 50 ? substr($changes, 0, 50) . '...' : $changes;
                    })
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return is_array($state) ? json_encode($state, JSON_PRETTY_PRINT) : null;
                    })
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('log_name')
                    ->label('Log Type')
                    ->options([
                        'chinook_artists' => 'Artists',
                        'chinook_albums' => 'Albums',
                        'chinook_tracks' => 'Tracks',
                        'chinook_playlists' => 'Playlists',
                        'chinook_purchases' => 'Purchases',
                        'chinook_media' => 'Media',
                        'chinook_taxonomy' => 'Taxonomy',
                    ]),

                SelectFilter::make('subject_type')
                    ->label('Model Type')
                    ->options([
                        'App\Models\ChinookArtist' => 'Artist',
                        'App\Models\ChinookAlbum' => 'Album',
                        'App\Models\ChinookTrack' => 'Track',
                        'App\Models\ChinookPlaylist' => 'Playlist',
                        'App\Models\ChinookCustomer' => 'Customer',
                    ]),

                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Until Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s'); // Auto-refresh every 30 seconds
    }
}
````
</augment_code_snippet>

---

**Navigation:** [Package Index](000-packages-index.md) | **Previous:** [Filament Shield Guide](240-bezhansalleh-filament-shield-guide.md) | **Next:** [Filament Health Guide](290-shuvroroy-filament-spatie-laravel-health-guide.md)

**Documentation Standards:** This document follows WCAG 2.1 AA accessibility guidelines and uses Laravel 12 modern syntax patterns with proper source attribution.
